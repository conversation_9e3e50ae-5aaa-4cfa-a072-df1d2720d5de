import 'package:flutter/material.dart';
import '../models/employee.dart';
import '../services/auth_service.dart';

class AuthProvider with ChangeNotifier {
  final AuthService _authService = AuthService();

  bool _isLoggedIn = false;
  Employee? _currentUser;
  bool _isLoading = false;

  bool get isLoggedIn => _isLoggedIn;
  Employee? get currentUser => _currentUser;
  bool get isLoading => _isLoading;

  Future<void> checkAuthStatus() async {
    _isLoading = true;
    notifyListeners();

    _isLoggedIn = await _authService.isLoggedIn();
    if (_isLoggedIn) {
      _currentUser = await _authService.getCurrentUser();
    }

    _isLoading = false;
    notifyListeners();
  }

  Future<bool> login(String username, String password) async {
    _isLoading = true;
    notifyListeners();

    final success = await _authService.login(username, password);
    if (success) {
      _isLoggedIn = true;
      _currentUser = await _authService.getCurrentUser();
    }

    _isLoading = false;
    notifyListeners();
    return success;
  }

  Future<void> logout() async {
    _isLoading = true;
    notifyListeners();

    await _authService.logout();
    _isLoggedIn = false;
    _currentUser = null;

    _isLoading = false;
    notifyListeners();
  }

  List<String> getAvailableUsernames() {
    return _authService.getAvailableUsernames();
  }
}
